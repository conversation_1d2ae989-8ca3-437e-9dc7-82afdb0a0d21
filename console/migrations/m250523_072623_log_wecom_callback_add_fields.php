<?php

use common\components\migrate\Migration;

/**
 * Class m250523_072623_log_wecom_callback_add_fields
 */
class m250523_072623_log_wecom_callback_add_fields extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = "
            ALTER TABLE `erp_log_wecom_callback`
            	ADD COLUMN `type` TINYINT(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '回调类型 1-企业微信回调 2-点击回调' AFTER `platform`,
            	ADD COLUMN `callback` VARCHAR(250) NOT NULL DEFAULT '' COMMENT '回调链接' COLLATE 'utf8_general_ci' AFTER `type`;
            ALTER TABLE `erp_log_wecom_callback`
	            ADD INDEX `callback` (`callback`);    
        ";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250523_072623_log_wecom_callback_add_fields cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250523_072623_log_wecom_callback_add_fields cannot be reverted.\n";

        return false;
    }
    */
}
