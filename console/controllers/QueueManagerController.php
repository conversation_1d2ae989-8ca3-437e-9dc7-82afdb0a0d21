<?php

namespace console\controllers;

use Yii;
use yii\console\Controller;
use yii\helpers\Console;

/**
 * 队列管理控制器
 * 
 * 支持管理多个队列实例，包括：
 * - 启动多个队列监听器
 * - 查看队列状态
 * - 清理队列数据
 */
class QueueManagerController extends Controller
{
    /**
     * 启动所有队列监听器
     * 
     * 使用方式：
     * php yii queue-manager/listen-all
     */
    public function actionListenAll()
    {
        $this->stdout("启动所有队列监听器...\n", Console::FG_GREEN);
        
        // 启动高优先级队列
        $this->stdout("启动高优先级队列监听器\n", Console::FG_YELLOW);
        $this->runCommand('php yii queue/listen --queue=queueHigh --verbose=1 > /dev/null 2>&1 &');
        
        // 启动普通优先级队列
        $this->stdout("启动普通优先级队列监听器\n", Console::FG_YELLOW);
        $this->runCommand('php yii queue/listen --queue=queueNormal --verbose=1 > /dev/null 2>&1 &');
        
        // 启动原有队列（兼容性）
        $this->stdout("启动原有队列监听器\n", Console::FG_YELLOW);
        $this->runCommand('php yii queue/listen --verbose=1 > /dev/null 2>&1 &');
        
        $this->stdout("所有队列监听器已启动\n", Console::FG_GREEN);
    }

    /**
     * 查看所有队列状态
     * 
     * 使用方式：
     * php yii queue-manager/status
     */
    public function actionStatus()
    {
        $this->stdout("队列状态信息:\n", Console::FG_GREEN);
        
        $queues = [
            'queue' => '默认队列',
            'queueHigh' => '高优先级队列', 
            'queueNormal' => '普通优先级队列'
        ];
        
        foreach ($queues as $queueName => $description) {
            $this->showQueueStatus($queueName, $description);
        }
        
        // 显示自定义队列状态
        $this->showCustomQueueStatus();
    }

    /**
     * 清理指定队列
     * 
     * 使用方式：
     * php yii queue-manager/clear queueHigh
     * php yii queue-manager/clear all
     */
    public function actionClear($queueName = 'all')
    {
        if ($queueName === 'all') {
            $this->stdout("清理所有队列...\n", Console::FG_YELLOW);
            $this->clearQueue('queue');
            $this->clearQueue('queueHigh');
            $this->clearQueue('queueNormal');
            $this->clearCustomQueue();
        } else {
            $this->stdout("清理队列: {$queueName}\n", Console::FG_YELLOW);
            $this->clearQueue($queueName);
        }
        
        $this->stdout("队列清理完成\n", Console::FG_GREEN);
    }

    /**
     * 运行一次所有队列
     * 
     * 使用方式：
     * php yii queue-manager/run-all
     */
    public function actionRunAll()
    {
        $this->stdout("执行所有队列任务...\n", Console::FG_GREEN);
        
        // 执行高优先级队列
        $this->stdout("执行高优先级队列\n", Console::FG_YELLOW);
        $this->runCommand('php yii queue/run --queue=queueHigh');
        
        // 执行普通优先级队列
        $this->stdout("执行普通优先级队列\n", Console::FG_YELLOW);
        $this->runCommand('php yii queue/run --queue=queueNormal');
        
        // 执行默认队列
        $this->stdout("执行默认队列\n", Console::FG_YELLOW);
        $this->runCommand('php yii queue/run');
        
        $this->stdout("所有队列任务执行完成\n", Console::FG_GREEN);
    }

    /**
     * 显示队列状态
     * 
     * @param string $queueName 队列名称
     * @param string $description 队列描述
     */
    private function showQueueStatus($queueName, $description)
    {
        try {
            $queue = Yii::$app->get($queueName);
            $channel = $queue->channel;
            
            $redis = Yii::$app->redis;
            
            // 获取各种状态的任务数量
            $waiting = $redis->llen("{$channel}.waiting");
            $delayed = $redis->zcard("{$channel}.delayed");
            $reserved = $redis->zcard("{$channel}.reserved");
            
            $this->stdout("\n{$description} ({$queueName}):\n", Console::FG_CYAN);
            $this->stdout("  Channel: {$channel}\n");
            $this->stdout("  等待任务: {$waiting}\n");
            $this->stdout("  延迟任务: {$delayed}\n");
            $this->stdout("  执行中任务: {$reserved}\n");
            
        } catch (\Exception $e) {
            $this->stdout("\n{$description} ({$queueName}): 获取状态失败\n", Console::FG_RED);
            $this->stdout("  错误: " . $e->getMessage() . "\n");
        }
    }

    /**
     * 显示自定义队列状态
     */
    private function showCustomQueueStatus()
    {
        try {
            $redis = Yii::$app->redis;
            
            $highCount = $redis->zcard('que.queueH');
            $normalCount = $redis->zcard('que.queueL');
            
            $this->stdout("\n自定义队列:\n", Console::FG_CYAN);
            $this->stdout("  高优先级 (que.queueH): {$highCount}\n");
            $this->stdout("  普通优先级 (que.queueL): {$normalCount}\n");
            
        } catch (\Exception $e) {
            $this->stdout("\n自定义队列: 获取状态失败\n", Console::FG_RED);
            $this->stdout("  错误: " . $e->getMessage() . "\n");
        }
    }

    /**
     * 清理指定队列
     * 
     * @param string $queueName 队列名称
     */
    private function clearQueue($queueName)
    {
        try {
            $queue = Yii::$app->get($queueName);
            $channel = $queue->channel;
            
            $redis = Yii::$app->redis;
            
            // 清理各种状态的任务
            $redis->del("{$channel}.waiting");
            $redis->del("{$channel}.delayed");
            $redis->del("{$channel}.reserved");
            $redis->del("{$channel}.messages");
            $redis->del("{$channel}.message_id");
            
            $this->stdout("  {$queueName} 清理完成\n");
            
        } catch (\Exception $e) {
            $this->stdout("  {$queueName} 清理失败: " . $e->getMessage() . "\n", Console::FG_RED);
        }
    }

    /**
     * 清理自定义队列
     */
    private function clearCustomQueue()
    {
        try {
            $redis = Yii::$app->redis;
            
            $redis->del('que.queueH');
            $redis->del('que.queueL');
            
            $this->stdout("  自定义队列清理完成\n");
            
        } catch (\Exception $e) {
            $this->stdout("  自定义队列清理失败: " . $e->getMessage() . "\n", Console::FG_RED);
        }
    }

    /**
     * 执行系统命令
     * 
     * @param string $command 命令
     */
    private function runCommand($command)
    {
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            $this->stdout("命令执行失败: {$command}\n", Console::FG_RED);
            foreach ($output as $line) {
                $this->stdout("  {$line}\n", Console::FG_RED);
            }
        }
    }
}
