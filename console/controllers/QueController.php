<?php

namespace console\controllers;

use Exception;
use Yii;

/**
 * 任务队列相关
 */
class QueController extends BaseController
{
    /**
     * 是否应该退出循环的标志
     * @var bool
     */
    protected $shouldExit = false;

    /**
     * 已处理的任务计数
     * @var int
     */
    protected $jobCount = 0;

    /**
     * 开始监听任务
     *
     * @return void
     */
    public function actionListenFork($channelName = null)
    {
        // 信号处理初始化
        $this->setupSignalHandlers();

        // 使用循环代替递归
        while (!$this->shouldExit) {
            $queue = Yii::$app->que;
            $ret = false;

            if ($channelName) {
                $ret = $queue->setChannel($channelName)->listenFork();
            } else {
                $ret = $queue->setImportant()->listenFork();
                if (!$ret) {
                    $ret = $queue->setNormal()->listenFork();
                }
            }

            if (!$ret) {
                sleep(1);
            } else {
                $this->jobCount++;
            }

            // 释放资源
            unset($queue);
            unset($ret);

            // 内存管理
            if (memory_get_usage() > 100 * 1024 * 1024) {
                Yii::warning('Queue worker memory limit reached, forcing garbage collection', 'actionListenFork');
                gc_collect_cycles();
            }

            // 可选：定期重启策略
            if ($this->jobCount > 2000) {
                Yii::info('Processed 2000 jobs, restarting worker for memory safety', 'actionListenFork');
                break; // 退出循环，让外部进程管理器重启
            }
        }
    }

    protected function setupSignalHandlers()
    {
        if (function_exists('pcntl_signal')) {
            // 设置信号处理
            pcntl_signal(SIGTERM, [$this, 'signalHandler']);
            pcntl_signal(SIGINT, [$this, 'signalHandler']);
        }
    }

    public function signalHandler($signal)
    {
        Yii::info("Received signal $signal, shutting down gracefully", 'actionListenFork');
        $this->shouldExit = true;
    }

    /**
     * 最终执行的命令
     */
    public function actionFork($channelName = 'queueL', $endTime = null)
    {
        $queue = Yii::$app->que;
        $queue->setChannel($channelName);
        $queue->runFork($endTime);
    }

    /**
     * 测试
     *
     * @return void
     */
    public function actionTest()
    {
        Yii::$app->que->setImportant()->push(new \common\components\dingtalk\DingNoticeJob([
            'title' => 'hhhh',
            'msg' => 'hhhh',
            'type' => 'happy',
        ]));
        Yii::$app->que->setNormal()->push(new \common\components\dingtalk\DingNoticeJob([
            'title' => 'lll',
            'msg' => 'lll',
            'type' => 'happy',
        ]));
    }
}
