<?php
return [
    'name' => 'erp',
    'version' => '2.0',
    'aliases' => [
        '@bower' => '@vendor/yidas/yii2-bower-asset/bower',
        '@npm'   => '@vendor/npm-asset',
    ],
    'vendorPath' => dirname(dirname(__DIR__)) . '/vendor',
    'language' => 'zh-CN',
    'sourceLanguage' => 'zh-cn',
    'timeZone' => 'Asia/Shanghai',
    'bootstrap' => [
        'queue', // 队列系统
        'common\components\Init', // 加载默认的配置
    ],
    'components' => [
        /** ------ 缓存 ------ **/
        'cache' => [
            'class' => 'common\components\RedisCache',
        ],
        /** ------ 格式化时间 ------ **/
        'formatter' => [
            'dateFormat' => 'yyyy-MM-dd',
            'datetimeFormat' => 'yyyy-MM-dd HH:mm:ss',
            'decimalSeparator' => ',',
            'thousandSeparator' => ' ',
            'currencyCode' => 'CNY',
        ],
        /** ------ 服务层 ------ **/
        'services' => [
            'class' => 'services\Application',
        ],
        /** ------ websocket redis配置 ------ **/
        'websocketRedis' => [
            'class' => 'yii\redis\Connection',
            'hostname' => '127.0.0.1',
            'port' => 6379,
            'database' => 1,
        ],
        /** ------ 网站碎片管理 ------ **/
        'debris' => [
            'class' => 'common\components\Debris',
        ],
        /** ------ 访问设备信息 ------ **/
        'mobileDetect' => [
            'class' => 'Detection\MobileDetect',
        ],
        /** ------ 队列设置 ------ **/
        'queue' => [
            'class' => 'yii\queue\redis\Queue',
            'redis' => 'redis', // 连接组件或它的配置
            'channel' => 'queue', // Queue channel key
            'as log' => 'yii\queue\LogBehavior',// 日志
        ],
        /** ------ 高优先级队列 ------ **/
        'queueHigh' => [
            'class' => 'yii\queue\redis\Queue',
            'redis' => 'redis',
            'channel' => 'queue_high', // 高优先级channel
            'as log' => 'yii\queue\LogBehavior',
        ],
        /** ------ 普通优先级队列 ------ **/
        'queueNormal' => [
            'class' => 'yii\queue\redis\Queue',
            'redis' => 'redis',
            'channel' => 'queue_normal', // 普通优先级channel
            'as log' => 'yii\queue\LogBehavior',
        ],
        /** ------ 队列设置 ------ **/
        'que' => [
            'class' => 'common\components\Queue',
        ],
        /** ------ 队列适配器 ------ **/
        'queueAdapter' => [
            'class' => 'common\components\QueueAdapter',
            'highPriorityQueue' => 'queueHigh',
            'normalPriorityQueue' => 'queueNormal',
        ],
        /** ------ 公用支付 ------ **/
        'pay' => [
            'class' => 'common\components\Pay',
        ],
        /** ------ 上传组件 ------ **/
        'uploadDrive' => [
            'class' => 'common\components\UploadDrive',
        ],
        /** ------ 快递查询 ------ **/
        'logistics' => [
            'class' => 'common\components\Logistics',
        ],
        /** ------ 二维码 ------ **/
        'qr' => [
            'class' => '\Da\QrCode\Component\QrCodeComponent',
            // ... 您可以在这里配置组件的更多属性
        ],
        /** ------ 微信SDK ------ **/
        'wechat' => [
            'class' => 'common\components\Wechat',
            'userOptions' => [],  // 用户身份类参数
            'sessionParam' => 'wechatUser', // 微信用户信息将存储在会话在这个密钥
            'returnUrlParam' => '_wechatReturnUrl', // returnUrl 存储在会话中
            'rebinds' => [
                'cache' => 'common\components\WechatCache',
            ]
        ],
        /** ------ 钉钉消息 ------ **/
        'notice' => [
            'class' => 'common\components\DingRobotNotice',
            // 主要钉钉群配置
            'main' => [
                'token' => '6fccc96a730ad0ad915992ca63d41a1ce42d294b32412b1be5c040635f613a1f',
                'secret' => 'SECb6bd93fd02fe8f530b1efdd9b6f2e3d92bbb461be1f5cab7bc0fb19862fbd877',
            ],
            // 次要钉钉群配置
            'sub' => [
                'token' => 'dc3479107dff97b13d46be6df558db148e4202826de9c4f66ac650e6e9d2ac7f',
                'secret' => 'SEC8796023aa406d6bc01ce137a86d15535d79f4a9d74034b89cf398f1213b0775a',
            ],
            // 主要消息等级设置（超过该等级的消息发送至主要钉钉群，反之发送到次要钉钉群）
            // error, warning 两个等级发送至主要群
            // info, debug 两个等级发送至次要群
            'mainLevel' => \common\components\DingRobotNotice::WARNING_LEVEL,
            // 消息标签
            'tags' => [
                'ERP',
            ]
        ],
        /** 飞书消息 */
        'feishuNotice' => [
            'class' => 'common\components\feishu\FeishuRobotNotice'
        ]
    ],
];
