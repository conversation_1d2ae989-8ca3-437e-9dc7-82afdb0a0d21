<?php

namespace common\models\log;

use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use common\models\common\AdsAccountProgram;
use common\models\common\AdsAccountSub;
use common\enums\log\WecomCallbackTypeEnum;
use Exception;
use Yii;

/**
 * This is the model class for table "{{%log_wecom_callback}}".
 *
 * @property int $id ID
 * @property string $wechat_unionid 微信unionid
 * @property string $campaign_id 推广计划ID
 * @property string $advertiser_id 广告主ID
 * @property string $platform 广告平台
 * @property int $type 回调类型 1-企业微信回调 2-点击回调
 * @property string $callback 回调链接
 * @property string $content 回调内容
 * @property int $created_at 创建时间
 */
class WecomCallback extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%log_wecom_callback}}';
    }

    public static function getDb()
    {
        return Yii::$app->erp_log2;
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['wechat_unionid', 'campaign_id', 'advertiser_id', 'platform'], 'string', 'max' => 60],
            [['type'], 'integer', 'min' => 0],
            [['type'], 'in', 'range' => WecomCallbackTypeEnum::getKeys()],
            [['callback'], 'string', 'max' => 250],
            [['content', 'wechat_unionid', 'campaign_id', 'advertiser_id', 'platform', 'callback'], 'trim'],
            [['content', 'wechat_unionid', 'campaign_id', 'advertiser_id', 'platform'], 'string'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'wechat_unionid' => '微信unionid',
            'campaign_id' => '推广计划ID',
            'content' => '回调内容',
            'advertiser_id' => '广告主ID',
            'platform' => '广告平台',
            'type' => '回调类型',
            'callback' => '回调链接',
            'created_at' => '创建时间',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['wechat_unionid', 'campaign_id', 'content', 'advertiser_id', 'platform', 'type', 'callback'],
        ];
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            if (!$this->created_at) {
                $this->created_at = time();
            }
        }
        return parent::beforeSave($isInsert);
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public static function push($content)
    {
        try {
            $model = new self();
            $model->wechat_unionid = $content['wechat_union_id'];
            $model->campaign_id = $content['campaign_id'];
            if (isset($content['advertiser_id']) && $content['advertiser_id']) {
                $model->advertiser_id = $content['advertiser_id'];
                $platform = AdsAccountSub::find()->alias('as')
                    ->select('a.platform')
                    ->leftJoin(['{{%ads_account}} a'], 'a.id = as.td_id')
                    ->where(['as.sub_advertiser_id' => $content['advertiser_id']])
                    ->scalar();
                $model->platform = $platform ?: '';
            } else {
                $ads = AdsAccountProgram::find()->alias('ap')
                    ->select('as.sub_advertiser_id,a.platform')
                    ->leftJoin(['{{%ads_account_sub}} as'], 'as.id = ap.ads_sub_id')
                    ->leftJoin(['{{%ads_account}} a'], 'a.id = as.td_id')
                    ->where(['ap.campaign_id' => $content['campaign_id']])
                    ->asArray()
                    ->one();
                if (empty($ads)) {
                    $model->advertiser_id = '';
                    $model->platform = '';
                } else {
                    $model->advertiser_id = $ads['sub_advertiser_id'];
                    $model->platform = $ads['platform'];
                }
            }
            $model->content = json_encode($content);
            $model->callback = $content['callback'] ?? '';
            $model->type = $content['type'] ?? WecomCallbackTypeEnum::UNKNOWN;
            if (!$model->save()) {
                throw new Exception(current($model->getFirstErrors()));
            }
        } catch (Exception $e) {
            Yii::$app->notice->important('新16上报成功回调日志保存失败', '', [
                'content' => $content,
                'message' => $e->getMessage(),
            ]);
        }
    }
}
