<?php

namespace common\models\common;

use common\enums\AdsAccountSubStatusEnum;
use common\enums\AdsAccountWayEnum;
use common\enums\ReportEventTargetEnum;
use common\enums\WhetherEnum;
use common\models\promote\AdsMainBody;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "{{%ads_account_sub}}".
 *
 * @property int $id
 * @property int $td_id 广告主账户id
 * @property string $sub_advertiser_name 广告子账户名称
 * @property string $sub_advertiser_id 广告子账户id
 * @property int $promote_id 渠道ID
 * @property int $link_id 链路ID
 * @property int $project_id 项目ID
 * @property int $direction_id 定向ID
 * @property int $responsible_id 责任人ID
 * @property int $agent_id 代理商ID
 * @property double $rebates 返点数
 * @property int $way 录入类型：1 自动 2 手动
 * @property int $status 状态：0禁用 1启用 2备用
 * @property int $report_event 上报事件：0 表单提交 1 添加企业微信
 * @property int $main_body_id 账户主体ID
 * @property int $pass_back_rate 回传比例
 * @property int $is_heavy_powder_report 重粉是否上报
 * @property int $dept_id 部门ID
 * @property int $entity_id 企业ID
 * @property int $created_by 创建人
 * @property int $updated_by 更新人
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 */
class AdsAccountSub extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%ads_account_sub}}';
    }

    /**
     * @return array
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class
            ]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['td_id'], 'required'],
            [['td_id', 'promote_id', 'link_id', 'project_id', 'direction_id', 'responsible_id', 'agent_id', 'way', 'status', 'report_event', 'main_body_id', 'dept_id', 'entity_id', 'created_by', 'updated_by', 'created_at', 'updated_at'], 'integer'],
            [['rebates'], 'number'],
            [['sub_advertiser_name', 'sub_advertiser_id'], 'string', 'max' => 255],
            ['status', 'in', 'range' => AdsAccountSubStatusEnum::getKeys()],
            ['report_event', 'in', 'range' => ReportEventTargetEnum::getKeys()],
            ['way', 'in', 'range' => AdsAccountWayEnum::getKeys()],
            [['pass_back_rate'], 'integer', 'min' => 0, 'message' => '定金回传比例必须大于等于0,0是都上报'],
            [['heavy_powder_rate'], 'integer', 'min' => 0, 'message' => '重粉回传比例必须大于等于0,0是都上报'],
            ['is_heavy_powder_report', 'in', 'range' => WhetherEnum::getKeys()]
        ];
    }

    /**
     * 场景
     * default 默认场景；
     * auto_create 自动拉创建账号；
     * manual_create 手动拉创建账号；
     * manual_edit_create 修改手动创建账号信息；
     * auto_edit_create 修改自动拉取账号信息；
     * status 修改状态；
     * set_agent 修改代理商；
     * set_rebates 修改返点；
     *
     * @return array
     */
    public function scenarios()
    {
        return [
            'default' => ['td_id', 'sub_advertiser_name', 'sub_advertiser_id', 'status', 'way', 'report_event', 'main_body_id', 'pass_back_rate', 'heavy_powder_rate', 'is_heavy_powder_report'],
            'auto_create' => ['td_id', 'status', 'sub_advertiser_name', 'sub_advertiser_id', 'way', 'report_event', 'main_body_id', 'pass_back_rate', 'heavy_powder_rate', 'is_heavy_powder_report'],
            'manual_create' => ['sub_advertiser_name', 'sub_advertiser_id', 'responsible_id', 'promote_id', 'main_body_id', 'link_id', 'project_id', 'direction_id', 'report_event', 'pass_back_rate', 'heavy_powder_rate', 'is_heavy_powder_report'],
            'manual_edit_create' => ['sub_advertiser_name', 'sub_advertiser_id', 'responsible_id', 'promote_id', 'main_body_id', 'link_id', 'project_id', 'direction_id', 'status', 'report_event', 'pass_back_rate', 'heavy_powder_rate', 'is_heavy_powder_report'],
            'auto_edit_create' => ['responsible_id', 'link_id', 'project_id', 'direction_id', 'status', 'report_event', 'main_body_id', 'pass_back_rate', 'heavy_powder_rate', 'is_heavy_powder_report'],
            'status' => ['status'],
            'set_agent' => ['agent_id'],
            'set_rebates' => ['rebates']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'td_id' => '广告主账户',
            'sub_advertiser_name' => '账户名称',
            'sub_advertiser_id' => '第三方ID',
            'promote_id' => '渠道',
            'link_id' => '链路',
            'project_id' => '项目',
            'direction_id' => '定向',
            'responsible_id' => '责任人',
            'agent_id' => '代理商',
            'rebates' => '返点数',
            'way' => '录入类型',
            'pass_back_rate' => '定金回传比例',
            'heavy_powder_rate' => '重粉回传比例',
            'is_heavy_powder_report' => '重粉是否上报',
            'dept_id' => '部门ID',
            'status' => '状态',
            'report_event' => '上报事件',
            'main_body_id' => '账户主体ID',
            'entity_id' => '企业ID',
            'created_by' => '创建人',
            'updated_by' => '更新人',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
        ];
    }

    /**
     * 批量修改子账户人员归属部门
     *
     * @param $dept_id
     * @param $responsible_id
     * @return bool
     */
    public static function updateAccountSubDept($responsible_id, $dept_id)
    {
        if (empty($responsible_id)) return false;
        self::updateAll(['dept_id' => $dept_id], ['and', ['=', 'responsible_id', $responsible_id], ['<>', 'dept_id', $dept_id]]);

        return true;
    }

    public function getAdsAccount()
    {
        return $this->hasOne(AdsAccount::class, ['id' => 'td_id']);
    }

    public static function getMainBodyIdByName($name, $entity_id)
    {
        if (empty($name) || empty($entity_id)) {
            return 0;
        }

        $list = AdsMainBody::find()->select('id,name')->where(['entity_id' => $entity_id])->cache(300)->asArray()->all();
        if (empty($list)) {
            return 0;
        }

        $main_body_id = 0;
        foreach ($list as $v) {
            if (strpos($name, $v['name']) === false) {
                continue;
            }
            $main_body_id = $v['id'];
            break;
        }

        return $main_body_id;
    }
}
