<?php

namespace common\components;

use Yii;
use yii\base\Component;

/**
 * 队列适配器 - 用于从自定义队列迁移到Yii2官方队列
 * 
 * 使用方式：
 * 1. 替换 Yii::$app->que 为 Yii::$app->queueAdapter
 * 2. 保持原有的API调用方式不变
 * 3. 内部自动路由到对应的官方队列实例
 */
class QueueAdapter extends Component
{
    /**
     * 高优先级队列组件名
     */
    public $highPriorityQueue = 'queueHigh';
    
    /**
     * 普通优先级队列组件名
     */
    public $normalPriorityQueue = 'queueNormal';
    
    /**
     * 当前选择的优先级
     */
    private $currentPriority = 'normal';
    
    /**
     * 延迟时间
     */
    private $delay = 0;

    /**
     * 设置为重要队列
     * 
     * @return $this
     */
    public function setImportant()
    {
        $this->currentPriority = 'high';
        return $this;
    }

    /**
     * 设置为普通队列
     * 
     * @return $this
     */
    public function setNormal()
    {
        $this->currentPriority = 'normal';
        return $this;
    }

    /**
     * 设置延迟时间
     * 
     * @param int $delay 延迟秒数
     * @return $this
     */
    public function delay($delay)
    {
        $this->delay = $delay;
        return $this;
    }

    /**
     * 推送任务到队列
     * 
     * @param mixed $job 任务对象
     * @return mixed 任务ID
     */
    public function push($job)
    {
        $queue = $this->getQueue();
        
        if ($this->delay > 0) {
            $result = $queue->delay($this->delay)->push($job);
            $this->delay = 0; // 重置延迟时间
        } else {
            $result = $queue->push($job);
        }
        
        $this->currentPriority = 'normal'; // 重置优先级
        return $result;
    }

    /**
     * 检查任务是否已存在（兼容性方法）
     * 
     * 注意：Yii2官方队列不支持此功能，这里提供兼容性实现
     * 可以通过缓存来模拟去重功能
     * 
     * @param mixed $job 任务对象
     * @return bool
     */
    public function has($job)
    {
        // 生成任务的唯一标识
        $jobKey = $this->generateJobKey($job);
        $cacheKey = "queue_dedup:{$this->currentPriority}:{$jobKey}";
        
        return Yii::$app->cache->exists($cacheKey);
    }

    /**
     * 设置任务去重标记
     * 
     * @param mixed $job 任务对象
     * @param int $ttl 过期时间（秒）
     */
    public function setJobDedup($job, $ttl = 3600)
    {
        $jobKey = $this->generateJobKey($job);
        $cacheKey = "queue_dedup:{$this->currentPriority}:{$jobKey}";
        
        Yii::$app->cache->set($cacheKey, 1, $ttl);
    }

    /**
     * 获取当前队列实例
     * 
     * @return \yii\queue\Queue
     */
    private function getQueue()
    {
        $componentName = $this->currentPriority === 'high' 
            ? $this->highPriorityQueue 
            : $this->normalPriorityQueue;
            
        return Yii::$app->get($componentName);
    }

    /**
     * 生成任务的唯一标识
     * 
     * @param mixed $job 任务对象
     * @return string
     */
    private function generateJobKey($job)
    {
        if (is_object($job) && method_exists($job, 'getJobKey')) {
            return $job->getJobKey();
        }
        
        // 使用序列化后的MD5作为默认标识
        return md5(serialize($job));
    }

    /**
     * 兼容原有的setChannel方法
     * 
     * @param string $channelName
     * @return $this
     */
    public function setChannel($channelName)
    {
        // 根据channel名称映射到优先级
        if (in_array($channelName, ['queueH', 'high', 'important'])) {
            $this->setImportant();
        } else {
            $this->setNormal();
        }
        
        return $this;
    }
}
