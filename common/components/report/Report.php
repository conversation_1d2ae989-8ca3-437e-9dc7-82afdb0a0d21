<?php

namespace common\components\report;

use common\helpers\ArrayHelper;
use common\helpers\DateHelper;
use common\models\backend\order\OrderHeader;
use common\models\backendapi\PromoteChannel;
use common\models\common\AdsAccountSub;
use common\models\common\ApiLog;
use common\models\wxcom\CusCustomerUser;
use Exception;
use Yii;

/**
 * 上报基础类
 */
class Report
{
    public $channel = null;
    public $order = null;
    public $reportType = '';
    public $thirdResult = null;
    public $cusCustomerUser = null;

    /**
     * 构造函数
     *
     * @param PromoteChannel $channel
     */
    public function __construct(PromoteChannel $channel)
    {
        $this->channel = $channel;
    }

    /**
     * 下订上报入口
     */
    public function orderCreatedReport(OrderHeader $order)
    {
        $this->reportType = 'orderCreatedReport';
        $this->order = $order;

        $result = false;
        $errMsg = '';

        try {
            // 执行下订前置事件，判断是否停止
            if (method_exists($this, 'beforeOrderCreatedReport')) {
                $isStop = false === call_user_func([$this, 'beforeOrderCreatedReport']);
                if ($isStop) {
                    return true;
                }
            }

            // 执行处理逻辑
            if (method_exists($this, 'onOrderCreatedReport')) {
                $result = call_user_func([$this, 'onOrderCreatedReport'], $order);
            }

            // 执行下订后置事件，处理返回结果
            if (method_exists($this, 'afterOrderCreatedReport')) {
                $result = call_user_func([$this, 'afterOrderCreatedReport'], $result);
                // 执行总后置事件，处理返回结果
            } elseif (method_exists($this, 'afterReport')) {
                $result = call_user_func([$this, 'afterReport'], $result);
            }
        } catch (Exception $e) {
            $errMsg = $e->getMessage();
        }

        $this->reportNotice($errMsg, !$result);

        Yii::info('订单号：' . $this->order->order_no . ',下订上报成功');

        $apiLog = new ApiLog();
        $apiLog->type = !!$result ? 'orderReport' : 'orderReportError';
        $apiLog->code = $this->order->order_no;
        $apiLog->content = $this->channel->name . '-下订上报';
        $apiLog->desc = $this->getCusUnionId();
        $apiLog->callback_pack = json_encode($this->thirdResult, JSON_UNESCAPED_UNICODE);

        if (!$apiLog->save()) {
            Yii::info('订单号：' . $this->order->order_no . ',下订上报成功,保存失败:' . current($apiLog->getFirstErrors()));
        }

        return $result;
    }

    /**
     * 下单上报前置事件
     *
     * @return boolean
     */
    protected function beforeOrderCreatedReport()
    {
        try {
            // 取不到客户联合id时，直接跳出
            $this->getCusUnionId();
            //判断下单渠道是否是客服
            if ($this->order->source_type != 1) {
                return false;
            }
            // cl为空不上报
            if (empty($this->order->customerUser->cl_code)) {
                return false;
            }
            // 没有上报配置不上报
            $reportArray = ArrayHelper::getValue($this, 'order.customerUser.landPageReport.report_array');
            if (!$reportArray) {
                return false;
            }
            $reportArray = json_decode($reportArray);
            if (!is_array($reportArray) || !count($reportArray)) {
                return false;
            }
        } catch (Exception $e) {
            return false;
        }

        return true;
    }

    /**
     * 上报后置事件
     * 
     * 将返回值保存到属性上，只返回失败标识
     *
     * @param mixed $result
     * @return boolean
     */
    protected function afterReport($result)
    {
        $this->thirdResult = $result;
        return $result !== false;
    }


    /**
     * 获取客户联合id
     * 
     * 默认取 unionid
     *
     * @return string
     */
    public function getCusUnionId()
    {
        return $this->getUnionId();
    }

    /**
     * 获取 callback
     *
     * @return string
     */
    protected function getCallback()
    {
        if (empty($this->order)) {
            throw new Exception('找不到订单信息');
        }
        if (empty($this->order->customerUser)) {
            throw new Exception('订单上找不到加粉信息');
        }
        if (empty($this->order->customerUser->callback)) {
            throw new Exception('加粉信息上找不到callback');
        }

        return $this->order->customerUser->callback;
    }

    /**
     * 获取 unionId
     *
     * @return string
     */
    protected function getUnionId()
    {
        if (empty($this->order)) {
            throw new Exception('找不到订单信息');
        }
        if (empty($this->order->customerUser)) {
            throw new Exception('订单上找不到加粉信息');
        }
        if (empty($this->order->customerUser->customer)) {
            throw new Exception('加粉信息上找不到客户信息');
        }
        if (empty($this->order->customerUser->customer->unionid)) {
            throw new Exception('客户信息上找不到unionId');
        }
        return $this->order->customerUser->customer->unionid;
    }

    /**
     * 获取企微开发者ID
     *
     * @return string
     */
    protected function getWxcomDeveloperId()
    {
        if (empty($this->order)) {
            throw new Exception('找不到订单信息');
        }
        if (empty($this->order->customerUser)) {
            throw new Exception('订单上找不到加粉信息');
        }
        if (empty($this->order->customerUser->customer)) {
            throw new Exception('加粉信息上找不到客户信息');
        }
        if (empty($this->order->customerUser->customer->com)) {
            throw new Exception('客户信息上找不到企微公司');
        }
        if (empty($this->order->customerUser->customer->com->developer_id)) {
            throw new Exception('企微公司找不到开发者ID');
        }
        return $this->order->customerUser->customer->com->developer_id;
    }

    protected function getCusUserIdByOrder()
    {
        if (empty($this->order)) {
            throw new Exception('找不到订单信息');
        }
        if (empty($this->order->customerUser)) {
            throw new Exception('订单上找不到加粉信息');
        }

        return $this->order->customerUser->id;
    }

    /**
     * 推广钉钉信息
     */
    protected function reportNotice(string $subTitle = '', $isError = false)
    {
        return;
        $title = $this->channel->name;
        $params = [
            '渠道' => $this->channel->name,
        ];
        switch ($this->reportType) {
            case 'orderCreatedReport':
                $title .= '下订上报';
                break;
            case 'cusUserCreatedReport':
                $title .= '下订上报';
                break;
        }
        $title .= $isError ? '失败' : '成功';

        $message = '';
        if ($subTitle) {
            $message .= $subTitle . "\n\n";
        }

        if ($this->order) {
            $params['订单编号'] = $this->order->order_no;
        }
        $params['时间'] = DateHelper::toDate(time());
        if ($isError) {
            $params['第三方平台结果'] = json_encode($this->thirdResult, JSON_UNESCAPED_UNICODE);
        }
        foreach ($params as $key => $value) {
            if (!$value) {
                continue;
            }
            $message .= "> {$key}： {$value} \n\n";
        }

        Yii::$app->notice->AdReport($title, '', $message);
    }

    //------------------------------------用户上报---------------------------------
    /**
     * 上报入口
     */
    public function cusUserCreatedReport(CusCustomerUser $cusCustomerUser)
    {
        $this->cusCustomerUser = $cusCustomerUser;

        $result = false;
        $errMsg = '';

        try {
            // 执行下订前置事件，判断是否停止
            if (method_exists($this, 'beforeCusUserCreatedReport')) {
                $isStop = false === call_user_func([$this, 'beforeCusUserCreatedReport']);
                if ($isStop) {
                    return true;
                }
            }

            // 执行处理逻辑
            if (method_exists($this, 'onCusUserCreatedReport')) {
                $result = call_user_func([$this, 'onCusUserCreatedReport'], $cusCustomerUser);
            }

            // 执行下订后置事件，处理返回结果
            if (method_exists($this, 'afterCusUserCreatedReport')) {
                $result = call_user_func([$this, 'afterCusUserCreatedReport'], $result);
                // 执行总后置事件，处理返回结果
            } elseif (method_exists($this, 'afterReport')) {
                $result = call_user_func([$this, 'afterReport'], $result);
            }

            Yii::info(['msg' => '用户id:' . $this->cusCustomerUser->id . ',重粉已订/重粉已做上报成功', 'result' => $this->thirdResult]);
        } catch (Exception $e) {
            $errMsg = $e->getMessage();
            Yii::info(['msg' => '用户id:' . $this->cusCustomerUser->id . ',重粉已订/重粉已做上报失败', 'result' => $this->thirdResult, 'errMsg' => $errMsg]);
        }

        return $result;
    }

    /**
     * 下单上报前置事件
     *
     * @return boolean
     */
    protected function beforeCusUserCreatedReport()
    {
        try {
            // 取不到客户联合id时，直接跳出
            $this->getCusUserUnionId();

            // cl为空不上报
            if (empty($this->cusCustomerUser->cl_code)) {
                return false;
            }
            // 没有上报配置不上报
            $reportArray = ArrayHelper::getValue($this, 'cusCustomerUser.landPageReport.report_array');
            if (!$reportArray) {
                return false;
            }
            $reportArray = json_decode($reportArray);
            if (!is_array($reportArray) || !count($reportArray)) {
                return false;
            }
        } catch (Exception $e) {
            return false;
        }

        return true;
    }

    /**
     * 获取客户联合id
     * 
     * 默认取 unionid
     *
     * @return string
     */
    public function getCusUserUnionId()
    {
        return $this->getUserUnionId();
    }

    protected function getCusUserId()
    {
        if (empty($this->cusCustomerUser)) {
            throw new Exception('找不到加粉信息');
        }

        return $this->cusCustomerUser->id;
    }

    protected function getCusUserCallback()
    {
        if (empty($this->cusCustomerUser)) {
            throw new Exception('找不到加粉信息');
        }
        if (empty($this->cusCustomerUser->callback)) {
            throw new Exception('加粉信息上找不到callback');
        }

        return $this->cusCustomerUser->callback;
    }

    /**
     * 获取 unionId
     *
     * @return string
     */
    protected function getUserUnionId()
    {
        if (empty($this->cusCustomerUser->customer)) {
            throw new Exception('加粉信息上找不到客户信息');
        }
        if (empty($this->cusCustomerUser->customer->unionid)) {
            throw new Exception('客户信息上找不到unionId');
        }
        return $this->cusCustomerUser->customer->unionid;
    }

    /**
     * 获取企微开发者ID
     *
     * @return string
     */
    protected function getCusUserWxcomDeveloperId()
    {
        if (empty($this->cusCustomerUser)) {
            throw new Exception('找不到加粉信息');
        }
        if (empty($this->cusCustomerUser->customer)) {
            throw new Exception('加粉信息上找不到客户信息');
        }
        if (empty($this->cusCustomerUser->customer->com)) {
            throw new Exception('客户信息上找不到企微公司');
        }
        if (empty($this->cusCustomerUser->customer->com->developer_id)) {
            throw new Exception('企微公司找不到开发者ID');
        }
        return $this->cusCustomerUser->customer->com->developer_id;
    }

    public function isCanReport()
    {
        if (empty($this->order)) {
            throw new Exception('找不到订单信息');
        }
        $customerUser = $this->order->customerUser;
        if (empty($customerUser)) {
            throw new Exception('订单上找不到加粉信息');
        }
        $sub_advertiser_id = $customerUser->sub_advertiser_id;

        if ($customerUser->is_order_report || empty($sub_advertiser_id)) {
            return true;
        }

        $pass_back_rate = AdsAccountSub::find()->select('pass_back_rate')->where(['sub_advertiser_id' => $sub_advertiser_id])->scalar();
        if ($pass_back_rate == 0) {
            return true;
        }

        $report_num = CusCustomerUser::find()
            ->andWhere(['sub_advertiser_id' => $sub_advertiser_id])
            ->andWhere(['<=', 'id', $customerUser->id])
            ->count();

        $units_digit = $report_num % $pass_back_rate;
        if ($units_digit == 0) {
            return false;
        }

        return true;
    }

    public function saveCustomerUserReport($id)
    {
        $model = CusCustomerUser::findOne($id);
        if ($model && $model->is_order_report == 0) {
            $model->is_order_report = 1;
            $model->save(false);
        }
    }

    public function isHeavyPowderCanReport()
    {
        if (empty($this->cusCustomerUser)) {
            throw new Exception('找不到加粉信息');
        }

        $sub_advertiser_id = $this->cusCustomerUser->sub_advertiser_id;
        if (empty($sub_advertiser_id)) {
            return false;
        }

        $accountSubInfo = AdsAccountSub::find()->select('is_heavy_powder_report,pass_back_rate')->where(['sub_advertiser_id' => $sub_advertiser_id])->one();
        if (empty($accountSubInfo) || $accountSubInfo->is_heavy_powder_report == 0 || $accountSubInfo->pass_back_rate < 1) {
            return false;
        }

        if ($this->reportType == 'heavyPowderDoReport') { //重粉已做
            if ($this->cusCustomerUser->is_heavy_powder_do_report == 1) {
                return true;
            }
        } else { //重粉已订
            if ($this->cusCustomerUser->is_heavy_powder_order_report == 1) {
                return true;
            }
        }

        if ($accountSubInfo->pass_back_rate == 0) {
            return true;
        }

        $report_num = CusCustomerUser::find()
            ->andWhere(['sub_advertiser_id' => $sub_advertiser_id])
            ->andWhere(['<=', 'id', $this->cusCustomerUser->id])
            ->count();

        $units_digit = $report_num % $accountSubInfo->pass_back_rate;

        if ($units_digit == 0) {
            return false;
        }

        return true;
    }

    public function saveCustUserHeavyPowderReport()
    {
        if ($this->reportType == 'heavyPowderDoReport') { //重粉已做
            if ($this->cusCustomerUser->is_heavy_powder_do_report == 1) {
                return true;
            }
            $this->cusCustomerUser->is_heavy_powder_do_report = 1;
        } else { //重粉已订
            if ($this->cusCustomerUser->is_heavy_powder_order_report == 1) {
                return true;
            }
            $this->cusCustomerUser->is_heavy_powder_order_report = 1;
        }

        $this->cusCustomerUser->save(false);
        return true;
    }
}
