<?php

namespace common\queues\examples;

use common\queues\BaseJobV2;
use Yii;
use Exception;

/**
 * 迁移示例：飞书通知任务
 * 
 * 原始任务：common\queues\FeishuNoticeJob
 * 迁移后：使用官方队列系统，支持重试和状态跟踪
 */
class MigratedFeishuNoticeJob extends BaseJobV2
{
    // 最大重试次数
    public $maxRetries = 3;
    
    // 接收者ID
    public $receive_id;
    
    // 消息类型
    public $msg_type;
    
    // 消息内容
    public $content;
    
    // 接收者ID类型
    public $receive_id_type = 'chat_id';

    /**
     * 执行任务
     * 
     * @param \yii\queue\Queue $queue
     * @return bool
     */
    public function run($queue)
    {
        try {
            // 检查是否发送过相同消息（5秒内）
            if ($this->isSendSameMsg()) {
                Yii::info('相同消息已发送，跳过', static::class);
                return true;
            }

            // 发送飞书消息
            $result = Yii::$app->feishuNotice->sendMessage(
                $this->receive_id,
                $this->msg_type,
                $this->content,
                $this->receive_id_type
            );

            if ($result) {
                Yii::info('飞书消息发送成功', static::class);
                return true;
            } else {
                throw new Exception('飞书消息发送失败');
            }

        } catch (Exception $e) {
            Yii::error('飞书消息发送异常: ' . $e->getMessage(), static::class);
            throw $e;
        }
    }

    /**
     * 检查是否发送过相同消息
     * 
     * @return bool
     */
    private function isSendSameMsg()
    {
        if (!is_string($this->content)) {
            $content = json_encode($this->content);
        } else {
            $content = $this->content;
        }
        
        $sameKey = "feishuLog:{$this->receive_id}:" . md5($content);
        $isSendTheSameMsg = Yii::$app->cache->exists($sameKey);
        Yii::$app->cache->set($sameKey, $this->content . '------time: ' . time(), 5);
        
        return $isSendTheSameMsg;
    }

    /**
     * 是否需要去重
     * 
     * @return bool
     */
    public function shouldDedup()
    {
        return true; // 飞书消息需要去重
    }

    /**
     * 获取任务唯一标识
     * 
     * @return string
     */
    public function getJobKey()
    {
        return parent::getJobKey() . $this->receive_id . ':' . md5($this->content);
    }

    /**
     * 添加任务 - 使用适配器（兼容原有API）
     * 
     * @param string $receive_id 接收者ID
     * @param string $msg_type 消息类型
     * @param mixed $content 消息内容
     * @param string $receive_id_type 接收者ID类型
     * @return mixed
     */
    public static function addJob($receive_id, $msg_type, $content, $receive_id_type = 'chat_id')
    {
        return static::addJobWithAdapter([
            'receive_id' => $receive_id,
            'msg_type' => $msg_type,
            'content' => $content,
            'receive_id_type' => $receive_id_type,
        ], 'normal'); // 使用普通优先级
    }

    /**
     * 添加高优先级任务
     * 
     * @param string $receive_id 接收者ID
     * @param string $msg_type 消息类型
     * @param mixed $content 消息内容
     * @param string $receive_id_type 接收者ID类型
     * @return mixed
     */
    public static function addHighPriorityJob($receive_id, $msg_type, $content, $receive_id_type = 'chat_id')
    {
        return static::addJobWithAdapter([
            'receive_id' => $receive_id,
            'msg_type' => $msg_type,
            'content' => $content,
            'receive_id_type' => $receive_id_type,
        ], 'high'); // 使用高优先级
    }

    /**
     * 直接使用官方队列添加任务
     * 
     * @param string $receive_id 接收者ID
     * @param string $msg_type 消息类型
     * @param mixed $content 消息内容
     * @param string $receive_id_type 接收者ID类型
     * @param int $delay 延迟时间
     * @return mixed
     */
    public static function addToQueue($receive_id, $msg_type, $content, $receive_id_type = 'chat_id', $delay = 0)
    {
        return static::addToNormalQueue([
            'receive_id' => $receive_id,
            'msg_type' => $msg_type,
            'content' => $content,
            'receive_id_type' => $receive_id_type,
        ], $delay);
    }
}
