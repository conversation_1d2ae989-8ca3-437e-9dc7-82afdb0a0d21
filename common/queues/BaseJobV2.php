<?php

namespace common\queues;

use Yii;
use yii\base\BaseObject;
use yii\queue\JobInterface;
use yii\queue\RetryableJobInterface;
use Exception;

/**
 * 新版BaseJob - 适配Yii2官方队列
 * 
 * 相比原有BaseJob的改进：
 * 1. 实现官方队列接口
 * 2. 支持重试机制
 * 3. 保持原有的限流功能
 * 4. 简化任务去重逻辑
 */
abstract class BaseJobV2 extends BaseObject implements JobInterface, RetryableJobInterface
{
    // 时间内限制的次数：默认0不限制
    public $times = 0;
    
    // 限制的时间范围：单位秒，默认一分钟
    public $time = 60;
    
    // 延迟时间：单位秒，默认10
    public $delay = 10;
    
    // 任务唯一标识
    public $jobId;
    
    // 重试次数
    public $retryTimes = 0;
    
    // 最大重试次数
    public $maxRetries = 3;

    public function __construct($params = [])
    {
        parent::__construct($params);
        $this->buildJobId();
    }

    /**
     * 构建任务ID
     */
    protected function buildJobId()
    {
        $this->jobId = time() . uniqid();
    }

    /**
     * 官方队列执行入口
     * 
     * @param \yii\queue\Queue $queue
     * @return void
     */
    public function execute($queue)
    {
        try {
            if ($this->checkIsLimit()) {
                throw new Exception('该任务被限流，延迟执行');
            }
            
            if (!$this->addRecord()) {
                throw new Exception('添加任务记录到redis时失败');
            }
            
            $ret = $this->run($queue);
            
            if (!$ret) {
                throw new Exception('任务执行失败');
            }
            
        } catch (Exception $e) {
            Yii::error("任务执行失败: " . $e->getMessage(), static::class);
            throw $e; // 重新抛出异常，让队列系统处理重试
        }
    }

    /**
     * 实际的业务逻辑执行方法
     * 子类需要实现此方法
     * 
     * @param \yii\queue\Queue $queue
     * @return bool
     */
    abstract public function run($queue);

    /**
     * 获取重试次数
     * 
     * @return int
     */
    public function getTtr()
    {
        return 300; // 5分钟超时
    }

    /**
     * 是否可以重试
     * 
     * @param int $attempt 当前尝试次数
     * @param \Exception $error 错误信息
     * @return bool
     */
    public function canRetry($attempt, $error)
    {
        return $attempt < $this->maxRetries;
    }

    /**
     * 判断是否限流
     * 
     * @return bool
     */
    public function checkIsLimit()
    {
        if (!$this->times) {
            return false;
        }

        $key = Yii::$app->cache->buildKey($this->getJobKey());
        $key = str_replace('\\', '\\\\', $key);
        $keys = Yii::$app->redis->keys("{$key}*");
        
        if (count($keys) >= $this->times) {
            return true;
        }

        return false;
    }

    /**
     * 添加执行记录
     * 
     * @return bool
     */
    public function addRecord()
    {
        return Yii::$app->cache->set($this->getJobKey() . uniqid(), 1, $this->time);
    }

    /**
     * 获取任务key标识
     * 
     * @return string
     */
    public function getJobKey()
    {
        return static::class . ':';
    }

    /**
     * 添加任务到高优先级队列
     * 
     * @param array $config 任务配置
     * @param int $delay 延迟时间
     * @return mixed
     */
    protected static function addToHighQueue($config, $delay = 0)
    {
        $job = new static($config);
        
        if ($delay > 0) {
            return Yii::$app->queueHigh->delay($delay)->push($job);
        }
        
        return Yii::$app->queueHigh->push($job);
    }

    /**
     * 添加任务到普通优先级队列
     * 
     * @param array $config 任务配置
     * @param int $delay 延迟时间
     * @return mixed
     */
    protected static function addToNormalQueue($config, $delay = 0)
    {
        $job = new static($config);
        
        if ($delay > 0) {
            return Yii::$app->queueNormal->delay($delay)->push($job);
        }
        
        return Yii::$app->queueNormal->push($job);
    }

    /**
     * 使用适配器添加任务（兼容原有API）
     * 
     * @param array $config 任务配置
     * @param string $priority 优先级 'high' 或 'normal'
     * @param int $delay 延迟时间
     * @return mixed
     */
    protected static function addJobWithAdapter($config, $priority = 'normal', $delay = 0)
    {
        $job = new static($config);
        $adapter = Yii::$app->queueAdapter;
        
        if ($priority === 'high') {
            $adapter->setImportant();
        } else {
            $adapter->setNormal();
        }
        
        if ($delay > 0) {
            $adapter->delay($delay);
        }
        
        // 检查是否已存在（如果需要去重）
        if (method_exists($job, 'shouldDedup') && $job->shouldDedup()) {
            if ($adapter->has($job)) {
                return null; // 任务已存在，不重复添加
            }
            $adapter->setJobDedup($job);
        }
        
        return $adapter->push($job);
    }

    /**
     * 是否需要去重
     * 子类可以重写此方法
     * 
     * @return bool
     */
    public function shouldDedup()
    {
        return false;
    }
}
