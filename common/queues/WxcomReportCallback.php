<?php

namespace common\queues;

use backendapi\services\wxcom\ReportService;
use common\services\TaskService;
use Exception;
use Yii;
use yii\base\BaseObject;

/**
 * 新16上报回调处理
 *
 * Class WxcomReportCallback
 * @package common\queues
 */
class WxcomReportCallback extends BaseObject implements \yii\queue\JobInterface
{
    public $retryTimes = 0;
    public $callbackParams;
    public $addTime = 0;

    public function execute($queue)
    {
        try {
            $this->run($queue);
        } catch (Exception $e) {
            $times = $this->retryTimes + 1;
            $this->retry();
        }
    }

    public function run($queue)
    {
        $reportService = new ReportService();
        $reportService->wxcomReport(
            $this->callbackParams['wechat_union_id'],
            $this->callbackParams['adgroup_id'],
            $this->callbackParams['trace_id'],
            $this->callbackParams['click_time'],
            $this->callbackParams['advertiser_id'],
            $this->callbackParams['callback']
        );
    }

    public function retry()
    {
        $this->retryTimes++;
        if ($this->retryTimes >= 3) {
            TaskService::addByJob($this, '新16上报回调逻辑处理报错；重试次数达到 3 次，不再重试');
            return true;
        }
        Yii::$app->queue->delay(30 * 60)->push(new self([
            'callbackParams' => $this->callbackParams,
            'retryTimes' => $this->retryTimes,
        ]));
    }

    public static function addJob($callbackParams)
    {
        // TODO zkx 验证参数
        Yii::$app->queue->delay(30)->push(new self([
            'callbackParams' => $callbackParams
        ]));
    }
}
