<?php

namespace common\enums\log;

use common\enums\BaseEnum;

class WecomCallbackTypeEnum extends BaseEnum
{
    const UNKNOWN = 0;
    const WECOM_CALLBACK = 1;
    const CLICK_CALLBACK = 2;

    public static function getMap(): array
    {
        return [
            self::UNKNOWN => '未知类型',
            self::WECOM_CALLBACK => '企业微信回调',
            self::CLICK_CALLBACK => '点击回调',
        ];
    }
}