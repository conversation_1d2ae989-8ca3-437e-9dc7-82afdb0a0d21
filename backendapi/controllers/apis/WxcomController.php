<?php

namespace backendapi\controllers\apis;

use backendapi\forms\wxcom\UserForm;
use backendapi\services\wxcom\ComService;
use backendapi\services\wxcom\CusAcquisitionService;
use backendapi\services\wxcom\CusMessageService;
use common\helpers\OtherHelper;
use common\helpers\ResultHelper;
use common\models\backend\Member;
use common\models\common\ApiLog;
use common\models\log\OceanengineCallback;
use common\models\log\WecomCallback;
use common\models\log\WxcomCallback;
use common\models\wxcom\Com;
use common\queues\WxcomReportCallback;
use EasyWeChat\Factory;
use EasyWeChat\Kernel\Messages\Message;
use EasyWeChat\Work\Server\Handlers\EchoStrHandler;
use common\enums\log\WecomCallbackTypeEnum;
use Exception;
use services\common\NewLogService;
use Yii;
use yii\web\Controller;

class WxcomController extends Controller
{
    public $enableCsrfValidation = false;
    public $comCode;

    /**
     * 企微加粉-回调事件
     */
    public function actionCallback($comCode)
    {
        $this->comCode = $comCode;

        // 企微回调配置验证
        $this->wxcomVerify();

        try {
            // 回调处理逻辑
            $options = ComService::getWxcomConfigByCodeForCom($comCode);
            $app = Factory::openWork($options);
            $server = $app->server;
            $server->push([$this, 'eventHandler'], Message::EVENT);
            $response = $server->serve();
            $response->sendContent();
        } catch (Exception $e) {
            Yii::$app->notice->{$comCode}('企微回调报错：', '', $e->getMessage());
        }
    }

    /**
     * 处理回调事件
     *
     * @param $message
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     */
    public function eventHandler($message)
    {
        $message['ComCode'] = $this->comCode;

        WxcomCallback::push($message);
        Yii::info('--------------回调开始-----------------','wxcomCallback');
        Yii::info(['message'=> json_encode($message,256)],'wxcomCallback');

        // 进程唯一
        $redis = Yii::$app->redis;
        $cacheData = $message;
        unset($cacheData['WelcomeCode']);
        $cacheKey = md5(json_encode($cacheData));
        if (!$redis->setnx($cacheKey, 1)) {
            exit;
        }
        $redis->expire($cacheKey, 60);

        //数据回调
        switch ($message['Event']) {
            // 内部通讯录
            case 'change_contact':
                switch ($message['ChangeType']) {
                    // 部门变动
                    case 'update_party':
                    case 'create_party':
                    case 'delete_party':
                        \backendapi\forms\wxcom\DepartmentForm::onEventCallback($message);
                        break;
                    // 成员变动
                    case 'update_user':
                    case 'create_user':
                    case 'delete_user':
                        \backendapi\forms\wxcom\UserForm::onEventCallback($message);
                        break;
                }
                break;
            // 客户标签
            case 'change_external_tag':
                \backendapi\forms\wxcom\CusTagForm::onEventCallback($message);
                break;
            // 外部通讯录（客户）
            case 'change_external_contact':
                switch ($message['ChangeType']) {
                    case 'add_external_contact':
                    case 'del_follow_user':
                    case 'del_external_contact':
                    case "add_half_external_contact":
                    case 'edit_external_contact':
                    case 'transfer_fail':
                        \backendapi\forms\wxcom\CusCustomerForm::onEventCallback($message);
                        break;
                }
                break;
            // 客服消息与事件
            case 'kf_msg_or_event':
                $com = Com::findOne(['code' => $this->comCode]);
                $message['corp_id'] = $com->corp_id;
                $message['cus_service_secret'] = $com->cus_service_secret;
                (new CusMessageService($com->corp_id, $com->cus_service_secret))->receive($message);
                break;

            //获客助手，客户开口事件
            case 'customer_acquisition':
                (new CusAcquisitionService($message))->onEventCallback();
                break;
        }
    }

    /**
     * 模拟回调测试
     *
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     */
    public function actionTest()
    {
        $this->comCode = 'zhimei';
        $message = [
            'ToUserName' => 'wwe265dcf35d8eda90',
            'FromUserName' => 'sys',
            'CreateTime' => '1640760369',
            'MsgType' => 'event',
            'Event' => 'change_external_tag',
            'ChangeType' => 'shuffle',
            'Id' => 'etHSdcEAAAAKZIpyGhICzuBf0RIwextQ',
        ];
        $this->eventHandler($message);
    }

    public function actionCallbackTest()
    {
        $message = Yii::$app->request->post('message');
        $this->comCode = $message['ComCode'] ?: 'zhimei';
        $this->eventHandler($message);
    }

    /**
     * 微信链路自动回复
     *
     * @throws \yii\db\Exception
     * @throws \yii\httpclient\Exception
     */
    public function actionWechatAutoReply()
    {
        $log = new NewLogService();
        $log->setName('Callback');
        $log->setLogPath(CusMessageService::getLogPath());

        $messageData = json_decode(file_get_contents("php://input"), true);
        $log->getLogger()->info('开始进入分配', [$messageData]);

        $ip = OtherHelper::getClientIp();
        // if ($ip != $_SERVER["REMOTE_ADDR"]) {
        //     $log->getLogger()->info('非法访问', ['IP：' . $ip]);
        //     exit;
        // }

        if (empty($messageData)) {
            $log->getLogger()->info('数据异常', ['IP：' . $ip]);
            exit;
        }

        $messageData['content'] = unserialize($messageData['content']);
        $messageData['message'] = unserialize($messageData['message']);
        (new CusMessageService($messageData['message']['corp_id'], $messageData['message']['cus_service_secret']))->sendAutoReply($messageData);
        print_r('处理成功！');
        exit;
    }

    /**
     * 企微回调配置验证
     *
     * @throws \Exception
     */
    protected function wxcomVerify()
    {
        $echostr = urldecode(Yii::$app->request->get('echostr'));
        if (!$echostr) return;

        // 回调链接配置验证
        $options = ComService::getWxcomConfigByCodeForCom($this->comCode);
        $app = Factory::work($options);
        $echoObject = (new EchoStrHandler($app))->handle();
        echo $echoObject->content;
        exit;
    }

    /**
     * 通过企业code获取企微配置
     *
     * @param string $comCode
     * @return array
     */
    protected function getWxcomOptionsByCode()
    {
        $config = \backendapi\services\EntityService::getWxcomInfoByCode($this->comCode);
        $options = [
            'corp_id' => $config['corp_id'],
            'secret' => $config['secret'],
            'token' => $config['token'],
            'aes_key' => $config['aes_key'],
        ];
        return $options;
    }

    /**
     * 新16上报成功后回调事件
     */
    public function actionWecomCallback()
    {
        $getData = Yii::$app->request->get();
        if(empty($getData)){
            echo 'success';
            exit;
        }
        //保存新16日志
        $getData['callback_type'] = '企业微信回调';
        $getData['type'] = WecomCallbackTypeEnum::WECOM_CALLBACK;
        WecomCallback::push($getData);
        WxcomReportCallback::addJob($getData);
        echo 'success';
        exit;
    }
    /**
     * --测试使用-
     * 
     * 新16上报成功后-点击回调事件
     */
    public function actionWecomClickCallback()
    {
        $getData = Yii::$app->request->get();
        if(empty($getData)){
            echo 'success';
            exit;
        }
        //保存新16日志
        $getData['callback_type'] = '点击回调';
        $getData['type'] = WecomCallbackTypeEnum::CLICK_CALLBACK;
        WecomCallback::push($getData);
        echo 'success';
        exit;
    }

    /**
     * 抖音回调
     */
    public function actionTiktokCallback()
    {
        $getData = Yii::$app->request->get();
        if (empty($getData)) {
            echo 'success';
            exit;
        }
        OceanengineCallback::push('click', $getData);
        echo 'success';
        exit;
    }

    /**
     * 企微登录模拟
     */
    public function actionLogin($comCode)
    {
        $code = Yii::$app->request->get('code');
        $host = Yii::$app->request->get('state');
        if (!$code || !$host) {
            echo '参数异常';
            exit;
        }
        $host = urldecode($host);
        $url = "http://{$host}?comCode={$comCode}&code={$code}";
        header("Location: {$url}");
    }

    /**
     * 企微聊天记录
     */
    public function actionChatRecord($comCode)
    {
        $this->comCode = $comCode;

        // 企微回调配置验证
        $this->wxcomVerify();

        $getData = Yii::$app->request->get();
        if (empty($getData)) return false;

        $apiLog = new ApiLog();
        $apiLog->type = 'chat_record';
        $apiLog->code = '200';
        $apiLog->content = json_encode($getData, JSON_UNESCAPED_UNICODE);
        $apiLog->save();
        return true;
    }

    public function actionSetWxcomQrcode()
    {
        $params = Yii::$app->request->post();
        try {
            $operator_id = $params['operator_id'] ?: -1;
            Yii::$app->user->identity = Member::findOne($operator_id);
            if (empty(Yii::$app->user->identity)) {
                throw new Exception('修改状态用户不存在');
            }

            UserForm::setWxcomQrcode($params['user_id']);

            Yii::$app->user->identity = null;
            return ResultHelper::json(200, '操作成功');
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }
}
