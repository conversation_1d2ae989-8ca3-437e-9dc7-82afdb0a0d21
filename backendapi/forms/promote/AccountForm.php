<?php

/**
 * Created by PhpStorm
 */

namespace backendapi\forms\promote;

use backendapi\services\MemberService;
use backendapi\services\promote\AgentService;
use backendapi\services\promote\ChannelService;
use backendapi\services\promote\PromoteLinkService;
use backendapi\services\promote\PromoteProjectService;
use common\enums\AdsAccountSubStatusEnum;
use common\enums\AdsAccountWayEnum;
use common\enums\StatusEnum;
use common\enums\WhetherEnum;
use common\models\common\AdsAccountSub;
use common\models\common\DepartmentAssignment;
use Yii;
use yii\db\Exception;

class AccountForm extends AdsAccountSub
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sub_advertiser_name', 'sub_advertiser_id', 'promote_id', 'responsible_id', 'agent_id', 'rebates'], 'required'],
            [['td_id', 'promote_id', 'link_id', 'project_id', 'responsible_id', 'agent_id', 'way', 'status', 'entity_id', 'created_by', 'updated_by', 'created_at', 'updated_at'], 'integer'],
            [['rebates'], 'number'],
            [['pass_back_rate','heavy_powder_rate'], 'integer', 'min' => 0],
            [['sub_advertiser_name', 'sub_advertiser_id'], 'string', 'max' => 255],
            ['status', 'in', 'range' => AdsAccountSubStatusEnum::getKeys()],
            ['way', 'in', 'range' => AdsAccountWayEnum::getKeys()],
            ['is_heavy_powder_report', 'in', 'range' => WhetherEnum::getKeys()],
            ['sub_advertiser_name', 'validateSubAdvertiserName'],
            ['sub_advertiser_id', 'validateSubAdvertiserID'],
            ['promote_id', 'validatePromote'],
            ['link_id', 'validateLink'],
            ['project_id', 'validateProject'],
            ['responsible_id', 'validateResponsible'],
            ['agent_id', 'validateAgent'],
            ['rebates', 'validateRebates'],
        ];
    }

    /**
     * 验证账号名称
     *
     * @param $attribute
     * @return bool
     */
    public function validateSubAdvertiserName($attribute)
    {
        $model = self::find()->select('id')->andWhere(['sub_advertiser_name' => $this->sub_advertiser_name])->one();
        if ((!$this->id && $model) || ($this->id && $model && $this->id != $model->id)) {
            $this->addError($attribute, '该账号名称已存在，请重新输入');
            return false;
        }

        return true;
    }

    /**
     * 验证第三方ID是否存在
     *
     * @param $attribute
     * @return bool
     */
    public function validateSubAdvertiserID($attribute)
    {
        $model = self::find()->select('id')->andWhere(['sub_advertiser_id' => $this->sub_advertiser_id])->one();
        if ((!$this->id && $model) || ($this->id && $model && $this->id != $model->id)) {
            $this->addError($attribute, '第三方ID已存在，请重新输入');
            return false;
        }

        return true;
    }

    /**
     * 判断渠道是否存在
     *
     * @param $attribute
     * @return bool
     */
    public function validatePromote($attribute)
    {
        $promote = ChannelService::find()->select('id,status')->where(['id' => $this->promote_id, 'entity_id' => Yii::$app->user->identity->current_entity_id])->one();
        if (empty($promote)) {
            $this->addError($attribute, '该渠道不存在,请重新选择');
            return false;
        }

        if ($promote->status != StatusEnum::ENABLED) {
            $this->addError($attribute, '该渠道已被禁用,请重新选择');
            return false;
        }

        return true;
    }

    /**
     * 判断链路是否存在
     *
     * @param $attribute
     * @return bool
     */
    public function validateLink($attribute)
    {
        if ($this->link_id == 0) return true;

        $link = PromoteLinkService::find()->select('id,status')->andWhere(['id' => $this->link_id])->one();
        if (empty($link)) {
            $this->addError($attribute, '该链路不存在,请重新选择');
            return false;
        }

        if ($link->status != StatusEnum::ENABLED) {
            $this->addError($attribute, '该链路已被禁用,请重新选择');
            return false;
        }

        return true;
    }

    /**
     * 判断项目是否存在
     *
     * @param $attribute
     * @return bool
     */
    public function validateProject($attribute)
    {
        if ($this->project_id == 0) return true;

        $project = PromoteProjectService::find()->select('id,status')->andWhere(['id' => $this->project_id])->one();
        if (empty($project)) {
            $this->addError($attribute, '该项目不存在,请重新选择');
            return false;
        }

        if ($project->status != StatusEnum::ENABLED) {
            $this->addError($attribute, '该项目已被禁用,请重新选择');
            return false;
        }
        return true;
    }

    /**
     * 判断负责人是否存在
     *
     * @param $attribute
     * @return bool
     */
    public function validateResponsible($attribute)
    {
        $responsible = MemberService::userIsPromotePerson($this->responsible_id);
        if (empty($responsible)) {
            $this->addError($attribute, '该负责人不是推广人员，请重新选择');
            return false;
        }

        $deptInfo = DepartmentAssignment::find()->select('dept_id')->where(['user_id' => $this->responsible_id])->one();
        if (empty($deptInfo)) {
            $this->addError($attribute, '该负责人所在部门为空，请联系管理员处理');
            return false;
        }
        $this->dept_id = $deptInfo->dept_id;

        return true;
    }

    /**
     * 判断代理商是否存在
     *
     * @param $attribute
     * @return bool
     */
    public function validateAgent($attribute)
    {
        $agent = AgentService::find()->select('id,status')->andWhere(['id' => $this->agent_id])->one();
        if (empty($agent)) {
            $this->addError($attribute, '该代理商不存在，请重新选择');
            return false;
        }
        if ($agent->status != StatusEnum::ENABLED) {
            $this->addError($attribute, '该代理商已被禁用，请重新选择或启用该代理商');
            return false;
        }

        return true;
    }

    /**
     * 判断返点是否大于0
     *
     * @param $attribute
     */
    public function validateRebates($attribute)
    {
        if (round($this->rebates, 3) <= 0) $this->addError($attribute, '返点必须大于0');
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public static function find()
    {
        return parent::find()->where(['entity_id' => Yii::$app->user->identity->current_entity_id]); // TODO: Change the autogenerated stub
    }

    /**
     * @param bool $insert
     * @return bool
     */
    public function beforeSave($insert)
    {
        if ($insert) {
            $this->created_by = Yii::$app->user->identity->id;
            $this->way = AdsAccountWayEnum::MANUAL;
            $this->entity_id = Yii::$app->user->identity->current_entity_id;
        }

        $this->updated_by = Yii::$app->user->identity->id;
        return parent::beforeSave($insert); // TODO: Change the autogenerated stub
    }

    /**
     * 修改代理商
     *
     * @param array $params
     * @return bool
     */
    public function changeAgent($params = [])
    {
        $tran = Yii::$app->db->beginTransaction();
        try {
            if (empty($params)) throw new Exception('请选择修改的内容');

            foreach ($params as $item) {
                if (!isset($item['id']) || !isset($item['agent_id'])) throw new Exception('参数格式有误，请联系管理员');
                /**@var $model self */
                $model = self::find()->andWhere(['id' => $item['id']])->one();
                if (!$model) throw new Exception('该账号不存在。');

                $agent = AgentForm::find()->select('rebate')->where(['id' => $item['agent_id']])->one();
                if (!$agent) throw new Exception('该代理不存在。');
                $model->rebates = $agent->rebate;

                $model->scenario = 'set_agent';
                $model->agent_id = $item['agent_id'];
                if (!$model->save()) throw new Exception(current($model->getFirstErrors()));
            }

            $tran->commit();
            return true;
        } catch (Exception $ex) {
            $tran->rollBack();
            $this->addError('', $ex->getMessage());
            return false;
        }
    }

    /**
     * 修改返点
     *
     * @param array $params
     * @return bool
     */
    public function changeRebates($params = [])
    {
        $tran = Yii::$app->db->beginTransaction();
        try {
            if (empty($params)) throw new Exception('请选择修改的内容');

            foreach ($params as $item) {
                if (!isset($item['id']) || !isset($item['rebates'])) throw new Exception('参数格式有误，请联系管理员');
                /**@var self $model  */
                $model = self::find()->andWhere(['id' => $item['id']])->one();
                if (!$model) throw new Exception('该账号不存在。');

                $model->scenario = 'set_rebates';
                $model->rebates = $item['rebates'];
                if (!$model->save()) throw new Exception(current($model->getFirstErrors()));
            }

            $tran->commit();
            return true;
        } catch (Exception $ex) {
            $tran->rollBack();
            $this->addError('', $ex->getMessage());
            return false;
        }
    }

    /**
     * 批量修改定向
     *
     * @return bool
     */
    public static function updateDirectionBatch($params)
    {
        $res = self::updateAll([
            'direction_id' => $params['direction_id'],
        ], [
            'in',
            'id',
            $params['ids']
        ]);

        return $res;
    }

    /**
     * 批量设置状态
     */
    public function setStatus($params = [])
    {
        $tran = Yii::$app->db->beginTransaction();
        try {
            if (empty($params)) throw new Exception('请选择修改的内容');
            if (!isset($params['ids']) || !is_array($params['ids'])) {
                throw new Exception('ids参数格式有误');
            }

            foreach ($params['ids'] as $id) {
                /**@var self  $model*/
                $model = self::find()->andWhere(['id' => $id])->one();
                if (!$model) throw new Exception('id:"' . $id . '"该账号id不存在。');

                $model->scenario = 'status';
                $model->status = $params['status'];
                if (!$model->save()) throw new Exception(current($model->getFirstErrors()));
            }

            $tran->commit();
            return true;
        } catch (Exception $ex) {
            $tran->rollBack();
            $this->addError('', $ex->getMessage());
            return false;
        }
    }
}
